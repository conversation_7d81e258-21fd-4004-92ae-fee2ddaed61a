import CryptoJs from "crypto-js";
import { computed, ref } from "vue";
import { defineStore } from "pinia";
import { useDictionary } from "@/stores/dictionary";
import { UserService } from "@modules/services/user";
import * as KycLimitModule from "@/modules/kycLimit";
import {
  messageCodes,
  userTeamMemberType,
  userTeamOwnerType,
} from "@/config/user";
import type {
  TCountry,
  TVerificationSteps,
} from "@/types/dictionary/dictionary.types";
import type {
  TUserFees,
  TUserInfo,
  TUserInfoPayload,
  TUserSummary,
} from "@/types/user/user.types";
import type { TErrorResponse } from "@modules/services/base/types";
import { setSentryUserId } from "@/helpers/sentry";
import { useBusinessDashboardGet } from "@/composable/API/useBusinessDashboardGet";
import type { TCountryResource } from "@/types/api/TCountryResource";
import type { TCurrencyResource } from "@/types/api/TCurrencyResource";
import { useUserInfoGet } from "@/composable/API/useUserInfoGet";
import { useUserSummaryGet } from "@/composable/API/useUserSummaryGet";
import { useUserFeesGet } from "@/composable/API/useUserFeesGet";
import { has, isEmpty } from "lodash";
import { useVerificationActualGet } from "@/composable/API/useVerificationActualGet";
import type { TVerificationMode } from "@/types/verification/verification";
import type { TCurrency } from "@/types/dictionary/currencies";
import { useBusinessSummaryGet } from "@/composable/API/useBusinessSummaryGet";
import { getFingerprintData } from "@/libs/fingerprint";
import { useUserEvents } from "@/composable/useUserEvents";

export type TUserStore = {
  // state
  user: Partial<TUserInfo>;
  loading: boolean;
  actual: TVerificationMode | "";
  userFees: Partial<TUserFees>;
  summary: Partial<TUserSummary>;

  // computed
  userKey: string;
  defaultCurrency: TCurrency | undefined;
  userCountry: TCountry["title"];
  userActualLimit: KycLimitModule.TKycLimitValidator | undefined;
  language: string;
  experiments: { [key: string]: boolean } | undefined;
  isTeamOwner: boolean | undefined;
  isTeamMember: boolean;
  isSuspicious: boolean;
  userCardLimit: number;
  hasMessenger: boolean;
  userIsWarn: boolean;
  isAuthUser: boolean;

  // actions
  getActualKycLevel: () => Promise<string | undefined>;
  updateActualKycLevel: () => Promise<void>;
  getUser: (
    config?: TUserInfoPayload
  ) => Promise<Partial<TUserInfo> | TErrorResponse>;
  getUserFees: () => Promise<void>;
  updateUserSummary: () => Promise<void>;
  getUserCountryCode: () => Promise<string>;

  [key: string]: any;
};

const SEND_INT = 1000 * 60 * 15;

const { startTrackingEvents, stopTrackingEvents } = useUserEvents();

export const useUserStore = defineStore("user", () => {
  // state
  const user = ref<TUserStore["user"]>({});
  const userMasterId = ref<number | null>(null);
  const userFees = ref<TUserStore["userFees"]>({});
  const loading = ref<TUserStore["loading"]>(false);
  const summary = ref<TUserStore["summary"]>({});
  const teamOwnerSummary = ref<TUserSummary | null>(null);
  const actual = ref<TUserStore["actual"]>("");
  const actualNum = ref<{
    cards?: number | null;
    deposit?: string | null;
  }>({
    cards: 0,
    deposit: "",
  });
  let userChecked: number | undefined = undefined;

  // computed
  const userKey = computed<TUserStore["userKey"]>(() => {
    const userEmail = user.value?.email || "";

    return CryptoJs.HmacMD5(userEmail, userEmail).toString();
  });

  const defaultCurrency: any = computed(() => {
    const { dictionary } = useDictionary();

    return dictionary?.currencies
      ?.filter((item: TCurrencyResource) => item.for_account)
      ?.find(
        (item: TCurrencyResource) => item.id === user.value.default_currency_id
      );
  });

  const userCountry = computed(() => {
    const { dictionary } = useDictionary();

    const userCountryLocal = dictionary?.countries?.find(
      (item: TCountryResource) =>
        item.id === user.value?.country_id ||
        item.id === user.value?.verification?.citizenship
    );

    return userCountryLocal?.title || "Afghanistan";
  });

  const language = computed(() => {
    let preLang: string = "";
    try {
      const lang: Array<string> = messageCodes.filter(
        (item: string): boolean => item == navigator.language.substring(0, 2)
      );

      preLang = lang.at(0) || "en";
    } catch (e) {
      preLang = "en";
    }

    return user.value?.language || preLang;
  });

  /**
   * @todo redundant property, check for duplicates
   */
  const experiments = computed(() => {
    return user.value?.experiments;
  });

  const isTeamOwner = computed<TUserStore["isTeamOwner"]>(
    // || undefined - is not mistake. Check team guard middleware
    () => user.value?.type === userTeamOwnerType || undefined
  );

  const isTeamMember = computed<TUserStore["isTeamMember"]>(
    () => user.value?.type === userTeamMemberType
  );

  const isSuspicious = computed<TUserStore["isSuspicious"]>(
    () => user.value?.is_suspicious_user ?? false
  );

  const userActualLimit = computed<TUserStore["userActualLimit"]>(() => {
    const { dictionary } = useDictionary();

    const finalActual = actual.value === "old" ? "corporate" : actual.value;
    const userLimit: TVerificationSteps | undefined =
      dictionary?.verificationSteps?.find(
        (v: Pick<TVerificationSteps, "slug">) => v.slug === finalActual
      );

    if (!userLimit) {
      return undefined;
    }

    return new KycLimitModule.KycLimit(userLimit);
  });

  /**
   * @todo the logic is being duplicated all across the project, needs replacement with the property from the store
   */
  const userIsWarn = computed<boolean>(() => !!user.value?.show_warn);

  /**
   * @deprecated is not being used
   */
  const userCardLimit = computed<TUserStore["userCardLimit"]>(() => {
    const availableCount =
      Number(userActualLimit.value?.userLimit?.card_limit) -
      Number(user.value?.summary?.cards_count);
    return userActualLimit.value ? availableCount || 0 : 1;
  });

  const hasTelegram = computed<boolean>(
    () => typeof user.value.telegram === "string"
  );

  const hasWhatsApp = computed<boolean>(
    () => typeof user.value.whatsapp === "string"
  );
  const hasMessenger = computed<boolean>(
    () => hasTelegram.value || hasWhatsApp.value
  );

  const isAuthUser = computed(() => !isEmpty(user.value));

  // actions
  const getActualKycLevel = async (): Promise<string | undefined> => {
    await updateActualKycLevel();
    return actual.value;
  };

  const updateActualKycLevel = async (): Promise<void> => {
    const { data: responseData, error: responseError } =
      await useVerificationActualGet();

    const verificationData = responseData.value?.data;

    if (!responseError) {
      console.error(
        "Stores/user->updateActualKycLevel error handled: !result",
        responseData
      );
      return;
    }
    if (verificationData) {
      actualNum.value.cards = verificationData.remaining_cards;
      actualNum.value.deposit = verificationData.remaining_deposit?.toString();
    }
    if (Array.isArray(verificationData)) {
      actual.value = "";
    } else {
      actual.value =
        (verificationData?.step?.toLowerCase() as TVerificationMode) || "";
    }
  };

  const sendFingerprint = async (): Promise<void> => {
    const fingerprintData = await getFingerprintData();
    await UserService.fingerprint(fingerprintData);
  };

  const getUser = async (
    config?: TUserInfoPayload
  ): Promise<Partial<TUserInfo> | TErrorResponse> => {
    loading.value = true;

    const { data: userInfoData } = await useUserInfoGet(config?.params);

    if (
      userInfoData.value?.data?.experiments.backtonovember &&
      [2, 4].includes(userInfoData.value?.data?.experiments.backtonovember) &&
      window.location.href !== `${window.location.origin}/a/app/dashboard`
    ) {
      window.location.href = `${window.location.origin}/a/app/dashboard`;
    }

    if (!userInfoData.value?.data) {
      loading.value = false;
      stopTrackingEvents();
      return userInfoData.value as TErrorResponse;
    }

    user.value = userInfoData.value.data;

    if (user.value.type === userTeamOwnerType || undefined) {
      const { data } = await useBusinessDashboardGet({ for: "master" });
      userMasterId.value =
        data.value?.data?.find(({ isMaster }) => isMaster)?.id ?? null;
    }

    if (user.value.uuid) {
      setSentryUserId(user.value.uuid);
    }

    if (userChecked === undefined || Date.now() - userChecked > SEND_INT) {
      userChecked = Date.now();
      await sendFingerprint();
      startTrackingEvents();
    }

    loading.value = false;

    return userInfoData.value.data;
  };

  const getUserFees = async (): Promise<void> => {
    loading.value = true;

    const { data: userFeesData } = await useUserFeesGet();

    if (!userFeesData.value?.data) {
      loading.value = false;
      console.error(
        "Stores/user->getUserFees error handled: !result.status",
        userFeesData.value
      );
      return;
    }

    userFees.value = userFeesData.value.data;

    loading.value = false;
  };

  const updateUserSummary = async (): Promise<void> => {
    const { data: userSummaryData } = await useUserSummaryGet();

    // TODO: find merge possibility between useBusinessSummaryGet and useUserSummaryGet and user.summary
    if (isTeamOwner.value) {
      const { data: businessSummaryData } = await useBusinessSummaryGet();

      if (businessSummaryData.value?.data) {
        teamOwnerSummary.value = businessSummaryData.value.data;
      }
    }

    if (!userSummaryData.value?.data) {
      console.error(
        "Stores/user->updateUserSummary error handled: !response.status",
        userSummaryData.value
      );
      return;
    }

    summary.value = userSummaryData.value.data;
  };

  /**
   * Get user country code
   * @return {string | undefined}
   */
  const getUserCountryCode = async (): Promise<string> => {
    let countryCode: string = "";

    const hasCountry = (
      value: Partial<TUserInfo> | TErrorResponse
    ): value is TUserInfo => {
      return has(value, "country_code");
    };

    if (hasCountry(user.value)) {
      countryCode = user.value?.country_code!;
    } else {
      const currentUser = await getUser();
      if (hasCountry(currentUser)) {
        countryCode = currentUser.country_code ?? "";
      }
    }

    return countryCode;
  };

  return {
    experiments,
    user,
    userMasterId,
    userKey,
    getUser,
    defaultCurrency,
    language,
    loading,
    isTeamOwner,
    actual,
    actualNum,
    getUserFees,
    getActualKycLevel,
    updateActualKycLevel,
    userFees,
    isTeamMember,
    isSuspicious,
    updateUserSummary,
    userCountry,
    summary,
    isAuthUser,
    userActualLimit,
    userCardLimit,
    teamOwnerSummary,
    userIsWarn,
    getUserCountryCode,
    hasMessenger,
  };
});
